/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 *
 * @format
 * @oncall react_native
 */

"use strict";

const { add, add0, add1, get0, get1, inc, neg, sub, sub1 } = require("../ob1");
const FORTY_TWO_0 = add0(42);
const FORTY_TWO_1 = add1(42);
module.exports = {
  testSafeOps() {
    add(FORTY_TWO_0, FORTY_TWO_0);
    add(FORTY_TWO_0, FORTY_TWO_1);
    add(FORTY_TWO_1, FORTY_TWO_0);
    sub(FORTY_TWO_1, FORTY_TWO_1);
    add(FORTY_TWO_0, 9000);
    add(FORTY_TWO_0, 9000);
    add(FORTY_TWO_1, 9000);
    sub(FORTY_TWO_1, 9000);
    get0(FORTY_TWO_0);
    get1(FORTY_TWO_1);
    neg(FORTY_TWO_0);
    add1(FORTY_TWO_0);
    sub1(FORTY_TWO_1);
    inc(FORTY_TWO_0);
    inc(FORTY_TWO_1);
  },
  testUnsafeOps() {
    // $FlowExpectedError - adding two 1-based offsets.
    add(FORTY_TWO_1, FORTY_TWO_1);

    // $FlowExpectedError - subtracting 1-based offset from 0-based offset.
    sub(FORTY_TWO_0, FORTY_TWO_1);

    // $FlowExpectedError - direct computations with offsets are disallowed.
    FORTY_TWO_0 - 1;

    // $FlowExpectedError - direct computations with offsets are disallowed.
    FORTY_TWO_1 - 1;

    // $FlowExpectedError - extracting a 1-based offset as a 0-based number
    get0(FORTY_TWO_1);

    // $FlowExpectedError - extracting a 0-based offset as a 1-based number
    get1(FORTY_TWO_0);

    // $FlowExpectedError - negating a 1-based offset
    neg(FORTY_TWO_1);

    // $FlowExpectedError - adding 1 to an offset that's already 1-based
    add1(FORTY_TWO_1);

    // $FlowExpectedError - subtracting 1 from an offset that's already 0-based
    sub1(FORTY_TWO_0);

    // $FlowExpectedError - extracting an arbitrary number as a 0-based number
    get0(42);

    // $FlowExpectedError - extracting an arbitrary number as a 1-based number
    get1(42);
  },
};
