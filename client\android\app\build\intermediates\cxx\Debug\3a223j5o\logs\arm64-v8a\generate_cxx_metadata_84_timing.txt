# C/C++ build system timings
generate_cxx_metadata
  create-invalidation-state 14ms
  generate-prefab-packages
    [gap of 37ms]
    exec-prefab 840ms
    [gap of 17ms]
  generate-prefab-packages completed in 894ms
  execute-generate-process
    exec-configure 684ms
    [gap of 201ms]
  execute-generate-process completed in 885ms
  [gap of 25ms]
  remove-unexpected-so-files 25ms
  [gap of 25ms]
generate_cxx_metadata completed in 1882ms

