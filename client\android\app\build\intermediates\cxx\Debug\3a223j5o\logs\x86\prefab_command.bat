@echo off
"C:\\Program Files\\Java\\jdk-17\\bin\\java" ^
  --class-path ^
  "C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\com.google.prefab\\cli\\2.1.0\\aa32fec809c44fa531f01dcfb739b5b3304d3050\\cli-2.1.0-all.jar" ^
  com.google.prefab.cli.AppKt ^
  --build-system ^
  cmake ^
  --platform ^
  android ^
  --abi ^
  x86 ^
  --os-version ^
  24 ^
  --stl ^
  c++_shared ^
  --ndk-version ^
  26 ^
  --output ^
  "C:\\Users\\<USER>\\AppData\\Local\\Temp\\agp-prefab-staging7147138349064569668\\staged-cli-output" ^
  "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\495638402d0fc5d3374d8dd464a0b611\\transformed\\react-android-0.77.0-debug\\prefab" ^
  "D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\29-05\\client\\android\\app\\build\\intermediates\\cxx\\refs\\react-native-reanimated\\356t3j4l" ^
  "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\8efc2e640de319a2aa5b8b521a15efc3\\transformed\\hermes-android-0.77.0-debug\\prefab" ^
  "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\9cd38d273aa80e0d66466b04adf6ea09\\transformed\\fbjni-0.7.0\\prefab"
