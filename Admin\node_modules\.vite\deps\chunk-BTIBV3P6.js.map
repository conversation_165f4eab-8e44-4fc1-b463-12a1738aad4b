{"version": 3, "sources": ["../../@radix-ui/react-use-controllable-state/src/useControllableState.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { useCallbackRef } from '@radix-ui/react-use-callback-ref';\n\ntype UseControllableStateParams<T> = {\n  prop?: T | undefined;\n  defaultProp?: T | undefined;\n  onChange?: (state: T) => void;\n};\n\ntype SetStateFn<T> = (prevState?: T) => T;\n\nfunction useControllableState<T>({\n  prop,\n  defaultProp,\n  onChange = () => {},\n}: UseControllableStateParams<T>) {\n  const [uncontrolledProp, setUncontrolledProp] = useUncontrolledState({ defaultProp, onChange });\n  const isControlled = prop !== undefined;\n  const value = isControlled ? prop : uncontrolledProp;\n  const handleChange = useCallbackRef(onChange);\n\n  const setValue: React.Dispatch<React.SetStateAction<T | undefined>> = React.useCallback(\n    (nextValue) => {\n      if (isControlled) {\n        const setter = nextValue as SetStateFn<T>;\n        const value = typeof nextValue === 'function' ? setter(prop) : nextValue;\n        if (value !== prop) handleChange(value as T);\n      } else {\n        setUncontrolledProp(nextValue);\n      }\n    },\n    [isControlled, prop, setUncontrolledProp, handleChange]\n  );\n\n  return [value, setValue] as const;\n}\n\nfunction useUncontrolledState<T>({\n  defaultProp,\n  onChange,\n}: Omit<UseControllableStateParams<T>, 'prop'>) {\n  const uncontrolledState = React.useState<T | undefined>(defaultProp);\n  const [value] = uncontrolledState;\n  const prevValueRef = React.useRef(value);\n  const handleChange = useCallbackRef(onChange);\n\n  React.useEffect(() => {\n    if (prevValueRef.current !== value) {\n      handleChange(value as T);\n      prevValueRef.current = value;\n    }\n  }, [value, prevValueRef, handleChange]);\n\n  return uncontrolledState;\n}\n\nexport { useControllableState };\n"], "mappings": ";;;;;;;;;;;AAAA,YAAuB;AAWvB,SAAS,qBAAwB;EAC/B;EACA;EACA,WAAW,MAAM;EAAC;AACpB,GAAkC;AAChC,QAAM,CAAC,kBAAkB,mBAAmB,IAAI,qBAAqB,EAAE,aAAa,SAAS,CAAC;AAC9F,QAAM,eAAe,SAAS;AAC9B,QAAM,QAAQ,eAAe,OAAO;AACpC,QAAM,eAAe,eAAe,QAAQ;AAE5C,QAAM,WAAsE;IAC1E,CAAC,cAAc;AACb,UAAI,cAAc;AAChB,cAAM,SAAS;AACf,cAAMA,SAAQ,OAAO,cAAc,aAAa,OAAO,IAAI,IAAI;AAC/D,YAAIA,WAAU,KAAM,cAAaA,MAAU;MAC7C,OAAO;AACL,4BAAoB,SAAS;MAC/B;IACF;IACA,CAAC,cAAc,MAAM,qBAAqB,YAAY;EACxD;AAEA,SAAO,CAAC,OAAO,QAAQ;AACzB;AAEA,SAAS,qBAAwB;EAC/B;EACA;AACF,GAAgD;AAC9C,QAAM,oBAA0B,eAAwB,WAAW;AACnE,QAAM,CAAC,KAAK,IAAI;AAChB,QAAM,eAAqB,aAAO,KAAK;AACvC,QAAM,eAAe,eAAe,QAAQ;AAEtC,EAAA,gBAAU,MAAM;AACpB,QAAI,aAAa,YAAY,OAAO;AAClC,mBAAa,KAAU;AACvB,mBAAa,UAAU;IACzB;EACF,GAAG,CAAC,OAAO,cAAc,YAAY,CAAC;AAEtC,SAAO;AACT;", "names": ["value"]}