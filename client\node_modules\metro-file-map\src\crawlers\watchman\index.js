"use strict";

var _constants = _interopRequireDefault(require("../../constants"));
var fastPath = _interopRequireWildcard(require("../../lib/fast_path"));
var _normalizePathSep = _interopRequireDefault(
  require("../../lib/normalizePathSep")
);
var _planQuery = require("./planQuery");
var _invariant = _interopRequireDefault(require("invariant"));
var path = _interopRequireWildcard(require("path"));
var _perf_hooks = require("perf_hooks");
function _getRequireWildcardCache(nodeInterop) {
  if (typeof WeakMap !== "function") return null;
  var cacheBabelInterop = new WeakMap();
  var cacheNodeInterop = new WeakMap();
  return (_getRequireWildcardCache = function (nodeInterop) {
    return nodeInterop ? cacheNodeInterop : cacheBabelInterop;
  })(nodeInterop);
}
function _interopRequireWildcard(obj, nodeInterop) {
  if (!nodeInterop && obj && obj.__esModule) {
    return obj;
  }
  if (obj === null || (typeof obj !== "object" && typeof obj !== "function")) {
    return { default: obj };
  }
  var cache = _getRequireWildcardCache(nodeInterop);
  if (cache && cache.has(obj)) {
    return cache.get(obj);
  }
  var newObj = {};
  var hasPropertyDescriptor =
    Object.defineProperty && Object.getOwnPropertyDescriptor;
  for (var key in obj) {
    if (key !== "default" && Object.prototype.hasOwnProperty.call(obj, key)) {
      var desc = hasPropertyDescriptor
        ? Object.getOwnPropertyDescriptor(obj, key)
        : null;
      if (desc && (desc.get || desc.set)) {
        Object.defineProperty(newObj, key, desc);
      } else {
        newObj[key] = obj[key];
      }
    }
  }
  newObj.default = obj;
  if (cache) {
    cache.set(obj, newObj);
  }
  return newObj;
}
function _interopRequireDefault(obj) {
  return obj && obj.__esModule ? obj : { default: obj };
}
/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 *
 * @format
 * @oncall react_native
 */

const watchman = require("fb-watchman");
const WATCHMAN_WARNING_INITIAL_DELAY_MILLISECONDS = 10000;
const WATCHMAN_WARNING_INTERVAL_MILLISECONDS = 20000;
const watchmanURL = "https://facebook.github.io/watchman/docs/troubleshooting";
function makeWatchmanError(error) {
  error.message =
    `Watchman error: ${error.message.trim()}. Make sure watchman ` +
    `is running for this project. See ${watchmanURL}.`;
  return error;
}
module.exports = async function watchmanCrawl({
  abortSignal,
  computeSha1,
  extensions,
  ignore,
  includeSymlinks,
  onStatus,
  perfLogger,
  previousState,
  rootDir,
  roots,
}) {
  abortSignal?.throwIfAborted();
  const client = new watchman.Client();
  abortSignal?.addEventListener("abort", () => client.end());
  perfLogger?.point("watchmanCrawl_start");
  const newClocks = new Map();
  let clientError;
  client.on("error", (error) => {
    clientError = makeWatchmanError(error);
  });
  const cmd = async (
    command,
    // $FlowFixMe[unclear-type] - Fix to use fb-watchman types
    ...args
  ) => {
    let didLogWatchmanWaitMessage = false;
    const startTime = _perf_hooks.performance.now();
    const logWatchmanWaitMessage = () => {
      didLogWatchmanWaitMessage = true;
      onStatus({
        type: "watchman_slow_command",
        timeElapsed: _perf_hooks.performance.now() - startTime,
        command,
      });
    };
    let intervalOrTimeoutId = setTimeout(() => {
      logWatchmanWaitMessage();
      intervalOrTimeoutId = setInterval(
        logWatchmanWaitMessage,
        WATCHMAN_WARNING_INTERVAL_MILLISECONDS
      );
    }, WATCHMAN_WARNING_INITIAL_DELAY_MILLISECONDS);
    try {
      const response = await new Promise((resolve, reject) =>
        // $FlowFixMe[incompatible-call] - dynamic call of command
        client.command([command, ...args], (error, result) =>
          error ? reject(makeWatchmanError(error)) : resolve(result)
        )
      );
      if ("warning" in response) {
        onStatus({
          type: "watchman_warning",
          warning: response.warning,
          command,
        });
      }
      // $FlowFixMe[incompatible-return]
      return response;
    } finally {
      // $FlowFixMe[incompatible-call] clearInterval / clearTimeout are interchangeable
      clearInterval(intervalOrTimeoutId);
      if (didLogWatchmanWaitMessage) {
        onStatus({
          type: "watchman_slow_command_complete",
          timeElapsed: _perf_hooks.performance.now() - startTime,
          command,
        });
      }
    }
  };
  async function getWatchmanRoots(roots) {
    perfLogger?.point("watchmanCrawl/getWatchmanRoots_start");
    const watchmanRoots = new Map();
    await Promise.all(
      roots.map(async (root, index) => {
        perfLogger?.point(`watchmanCrawl/watchProject_${index}_start`);
        const response = await cmd("watch-project", root);
        perfLogger?.point(`watchmanCrawl/watchProject_${index}_end`);
        const existing = watchmanRoots.get(response.watch);
        // A root can only be filtered if it was never seen with a
        // relative_path before.
        const canBeFiltered = !existing || existing.directoryFilters.length > 0;
        if (canBeFiltered) {
          if (response.relative_path) {
            watchmanRoots.set(response.watch, {
              watcher: response.watcher,
              directoryFilters: (existing?.directoryFilters || []).concat(
                response.relative_path
              ),
            });
          } else {
            // Make the filter directories an empty array to signal that this
            // root was already seen and needs to be watched for all files or
            // directories.
            watchmanRoots.set(response.watch, {
              watcher: response.watcher,
              directoryFilters: [],
            });
          }
        }
      })
    );
    perfLogger?.point("watchmanCrawl/getWatchmanRoots_end");
    return watchmanRoots;
  }
  async function queryWatchmanForDirs(rootProjectDirMappings) {
    perfLogger?.point("watchmanCrawl/queryWatchmanForDirs_start");
    const results = new Map();
    let isFresh = false;
    await Promise.all(
      Array.from(rootProjectDirMappings).map(
        async ([root, { directoryFilters, watcher }], index) => {
          // Jest is only going to store one type of clock; a string that
          // represents a local clock. However, the Watchman crawler supports
          // a second type of clock that can be written by automation outside of
          // Jest, called an "scm query", which fetches changed files based on
          // source control mergebases. The reason this is necessary is because
          // local clocks are not portable across systems, but scm queries are.
          // By using scm queries, we can create the haste map on a different
          // system and import it, transforming the clock into a local clock.
          const since = previousState.clocks.get(
            fastPath.relative(rootDir, root)
          );
          perfLogger?.annotate({
            bool: {
              [`watchmanCrawl/query_${index}_has_clock`]: since != null,
            },
          });
          const { query, queryGenerator } = (0, _planQuery.planQuery)({
            since,
            extensions,
            directoryFilters,
            includeSha1: computeSha1,
            includeSymlinks,
          });
          perfLogger?.annotate({
            string: {
              [`watchmanCrawl/query_${index}_watcher`]: watcher ?? "unknown",
              [`watchmanCrawl/query_${index}_generator`]: queryGenerator,
            },
          });
          perfLogger?.point(`watchmanCrawl/query_${index}_start`);
          const response = await cmd("query", root, query);
          perfLogger?.point(`watchmanCrawl/query_${index}_end`);

          // When a source-control query is used, we ignore the "is fresh"
          // response from Watchman because it will be true despite the query
          // being incremental.
          const isSourceControlQuery =
            typeof since !== "string" && since?.scm?.["mergebase-with"] != null;
          if (!isSourceControlQuery) {
            isFresh = isFresh || response.is_fresh_instance;
          }
          results.set(root, response);
        }
      )
    );
    perfLogger?.point("watchmanCrawl/queryWatchmanForDirs_end");
    return {
      isFresh,
      results,
    };
  }
  let removedFiles = new Map();
  const changedFiles = new Map();
  let results;
  let isFresh = false;
  let queryError;
  try {
    const watchmanRoots = await getWatchmanRoots(roots);
    const watchmanFileResults = await queryWatchmanForDirs(watchmanRoots);

    // Reset the file map if watchman was restarted and sends us a list of
    // files.
    if (watchmanFileResults.isFresh) {
      removedFiles = new Map(previousState.files);
      isFresh = true;
    }
    results = watchmanFileResults.results;
  } catch (e) {
    queryError = e;
  }
  client.end();
  if (results == null) {
    if (clientError) {
      perfLogger?.annotate({
        string: {
          "watchmanCrawl/client_error":
            clientError.message ?? "[message missing]",
        },
      });
    }
    if (queryError) {
      perfLogger?.annotate({
        string: {
          "watchmanCrawl/query_error":
            queryError.message ?? "[message missing]",
        },
      });
    }
    perfLogger?.point("watchmanCrawl_end");
    abortSignal?.throwIfAborted();
    throw (
      queryError ?? clientError ?? new Error("Watchman file results missing")
    );
  }
  perfLogger?.point("watchmanCrawl/processResults_start");
  for (const [watchRoot, response] of results) {
    const fsRoot = (0, _normalizePathSep.default)(watchRoot);
    const relativeFsRoot = fastPath.relative(rootDir, fsRoot);
    newClocks.set(
      relativeFsRoot,
      // Ensure we persist only the local clock.
      typeof response.clock === "string" ? response.clock : response.clock.clock
    );
    for (const fileData of response.files) {
      const filePath =
        fsRoot + path.sep + (0, _normalizePathSep.default)(fileData.name);
      const relativeFilePath = fastPath.relative(rootDir, filePath);
      const existingFileData = previousState.files.get(relativeFilePath);

      // If watchman is fresh, the removed files map starts with all files
      // and we remove them as we verify they still exist.
      if (isFresh && existingFileData && fileData.exists) {
        removedFiles.delete(relativeFilePath);
      }
      if (!fileData.exists) {
        // No need to act on files that do not exist and were not tracked.
        if (existingFileData) {
          // If watchman is not fresh, we will know what specific files were
          // deleted since we last ran and can track only those files.
          if (!isFresh) {
            removedFiles.set(relativeFilePath, existingFileData);
          }
        }
      } else if (!ignore(filePath)) {
        const { mtime_ms, size } = fileData;
        (0, _invariant.default)(
          mtime_ms != null && size != null,
          "missing file data in watchman response"
        );
        const mtime =
          typeof mtime_ms === "number" ? mtime_ms : mtime_ms.toNumber();
        if (
          existingFileData &&
          existingFileData[_constants.default.MTIME] === mtime
        ) {
          continue;
        }
        let sha1hex = fileData["content.sha1hex"];
        if (typeof sha1hex !== "string" || sha1hex.length !== 40) {
          sha1hex = undefined;
        }
        let symlinkInfo = 0;
        if (fileData.type === "l") {
          symlinkInfo = fileData["symlink_target"] ?? 1;
        }
        let nextData = ["", mtime, size, 0, "", sha1hex ?? null, symlinkInfo];
        if (
          existingFileData &&
          sha1hex != null &&
          existingFileData[_constants.default.SHA1] === sha1hex &&
          // File is still of the same type
          (existingFileData[_constants.default.SYMLINK] !== 0) ===
            (fileData.type === "l")
        ) {
          // Special case - file touched but not modified, so we can reuse the
          // metadata and just update mtime.
          nextData = [
            existingFileData[0],
            mtime,
            existingFileData[2],
            existingFileData[3],
            existingFileData[4],
            existingFileData[5],
            typeof symlinkInfo === "string" ? symlinkInfo : existingFileData[6],
          ];
        }
        changedFiles.set(relativeFilePath, nextData);
      }
    }
  }
  perfLogger?.point("watchmanCrawl/processResults_end");
  perfLogger?.point("watchmanCrawl_end");
  abortSignal?.throwIfAborted();
  return {
    changedFiles,
    removedFiles,
    clocks: newClocks,
  };
};
