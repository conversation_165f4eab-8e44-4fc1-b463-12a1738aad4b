# ninja log v5
2	23	0	D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-reanimated/android/.cxx/Debug/252f2v2v/armeabi-v7a/CMakeFiles/cmake.verify_globs	4bcba20779f7704f
1	2397	7702053567223012	src/main/cpp/worklets/CMakeFiles/worklets.dir/c373fb0cb0205ac8156540e3132b2685/Registries/WorkletRuntimeRegistry.cpp.o	122fd4b97a350da2
17	2477	7702053567952292	src/main/cpp/worklets/CMakeFiles/worklets.dir/7519ac08560a20b167be15fe9fbea7e4/cpp/worklets/Tools/ReanimatedJSIUtils.cpp.o	fdd3a7ccd93c6041
10	2829	7702053570708702	src/main/cpp/worklets/CMakeFiles/worklets.dir/7519ac08560a20b167be15fe9fbea7e4/cpp/worklets/Tools/JSISerializer.cpp.o	42acaec4cf86d5eb
38	3047	7702053572931447	src/main/cpp/worklets/CMakeFiles/worklets.dir/8381e4f2241b07a247a8edec969abc0e/Common/cpp/worklets/Tools/JSScheduler.cpp.o	6bf11cec3b8df909
13	3070	7702053572542982	src/main/cpp/worklets/CMakeFiles/worklets.dir/8381e4f2241b07a247a8edec969abc0e/Common/cpp/worklets/Tools/AsyncQueue.cpp.o	57fbc60230d110cd
20	3110	7702053574356574	src/main/cpp/worklets/CMakeFiles/worklets.dir/8381e4f2241b07a247a8edec969abc0e/Common/cpp/worklets/Tools/JSLogger.cpp.o	3898d5cce7865dad
52	3149	7702053574813345	src/main/cpp/worklets/CMakeFiles/worklets.dir/5902132a011a07009ebc4c1979b47fca/worklets/Tools/WorkletEventHandler.cpp.o	4ade60f24323d744
4	4124	7702053583629087	src/main/cpp/worklets/CMakeFiles/worklets.dir/c373fb0cb0205ac8156540e3132b2685/Registries/EventHandlerRegistry.cpp.o	2f01c92b6396be06
7	4405	7702053585877305	src/main/cpp/worklets/CMakeFiles/worklets.dir/7519ac08560a20b167be15fe9fbea7e4/cpp/worklets/SharedItems/Shareables.cpp.o	4922089c56c4b9e2
42	5288	7702053595216467	src/main/cpp/worklets/CMakeFiles/worklets.dir/7519ac08560a20b167be15fe9fbea7e4/cpp/worklets/Tools/ReanimatedVersion.cpp.o	feae1af7a859e2d4
2401	5336	7702053596577288	src/main/cpp/worklets/CMakeFiles/worklets.dir/8381e4f2241b07a247a8edec969abc0e/Common/cpp/worklets/Tools/UIScheduler.cpp.o	19c4692b0b177e8b
2478	6060	7702053603321831	src/main/cpp/worklets/CMakeFiles/worklets.dir/ff39682c307d56e3affbd2968b272f0a/ReanimatedHermesRuntime.cpp.o	9ba4e3fbeff7404d
3048	6068	7702053603862664	src/main/cpp/worklets/CMakeFiles/worklets.dir/c373fb0cb0205ac8156540e3132b2685/WorkletRuntime/ReanimatedRuntime.cpp.o	aea1780354ce324
3110	6388	7702053606823034	src/main/cpp/reanimated/CMakeFiles/reanimated.dir/bc997d19da1574dd9ca452f37fd86245/AnimatedSensorModule.cpp.o	510e6f64b7d3a3ad
2829	7578	7702053618638843	src/main/cpp/worklets/CMakeFiles/worklets.dir/c373fb0cb0205ac8156540e3132b2685/WorkletRuntime/WorkletRuntime.cpp.o	2d84a4693f48b89c
3071	9183	7702053634800916	src/main/cpp/worklets/CMakeFiles/worklets.dir/ff39682c307d56e3affbd2968b272f0a/WorkletRuntimeDecorator.cpp.o	a75fa30a66631480
3149	9358	7702053636778046	src/main/cpp/reanimated/CMakeFiles/reanimated.dir/5902132a011a07009ebc4c1979b47fca/reanimated/Fabric/PropsRegistry.cpp.o	1ece81d576e74279
9184	10398	7702053645483371	../../../../build/intermediates/cxx/Debug/252f2v2v/obj/armeabi-v7a/libworklets.so	1ea1b24e9074e6f
6061	11019	7702053652620686	src/main/cpp/reanimated/CMakeFiles/reanimated.dir/5384bd35cebe0339a8534868eb1525ec/LayoutAnimationsManager.cpp.o	6f40efbe446f6b97
4125	11779	7702053660696045	src/main/cpp/reanimated/CMakeFiles/reanimated.dir/903cdc38e100b3e8f688e82b49cafa56/Fabric/ReanimatedMountHook.cpp.o	bc62ac86fe756ca0
10399	12580	7702053668065507	src/main/cpp/reanimated/CMakeFiles/reanimated.dir/5902132a011a07009ebc4c1979b47fca/reanimated/Tools/FeaturesConfig.cpp.o	6cc91c63fd54995b
5288	12644	7702053669130412	src/main/cpp/reanimated/CMakeFiles/reanimated.dir/5384bd35cebe0339a8534868eb1525ec/LayoutAnimationsUtils.cpp.o	d5423689bbbca744
5337	13222	7702053674695780	src/main/cpp/reanimated/CMakeFiles/reanimated.dir/903cdc38e100b3e8f688e82b49cafa56/Fabric/ShadowTreeCloner.cpp.o	754e7dd60417f13d
4406	13488	7702053676354097	src/main/cpp/reanimated/CMakeFiles/reanimated.dir/903cdc38e100b3e8f688e82b49cafa56/Fabric/ReanimatedCommitHook.cpp.o	adc7cef66d72094d
11019	14648	7702053689165799	src/main/cpp/reanimated/CMakeFiles/reanimated.dir/0ece905a2c2bf93f9c78f535a359f32b/ReanimatedWorkletRuntimeDecorator.cpp.o	3019e9066b5a2bc7
9360	15338	7702053695960072	src/main/cpp/reanimated/CMakeFiles/reanimated.dir/0ece905a2c2bf93f9c78f535a359f32b/UIRuntimeDecorator.cpp.o	bfa98deb1a156f6a
11780	15787	7702053701050627	src/main/cpp/reanimated/CMakeFiles/reanimated.dir/de26f8cead47227ca676f03d1d571aaf/NativeReanimatedModuleSpec.cpp.o	fc45a4bd74bffa3
7578	16240	7702053704963868	src/main/cpp/reanimated/CMakeFiles/reanimated.dir/0ece905a2c2bf93f9c78f535a359f32b/RNRuntimeDecorator.cpp.o	ae0d2166f6f04120
15338	17354	7702053716559820	src/main/cpp/reanimated/CMakeFiles/reanimated.dir/android/PlatformLogger.cpp.o	67bb0b9d48eedeae
6068	18197	7702053724912222	src/main/cpp/reanimated/CMakeFiles/reanimated.dir/5384bd35cebe0339a8534868eb1525ec/LayoutAnimationsProxy.cpp.o	8d7b37e7503d7f4a
12581	18463	7702053728005558	src/main/cpp/reanimated/CMakeFiles/reanimated.dir/android/JNIHelper.cpp.o	a109f775e6ee2567
12716	18477	7702053728118073	src/main/cpp/reanimated/CMakeFiles/reanimated.dir/android/AndroidUIScheduler.cpp.o	12b67fec3a29d736
14648	19386	7702053737284939	src/main/cpp/reanimated/CMakeFiles/reanimated.dir/android/LayoutAnimations.cpp.o	f06b611947c053c9
6388	20636	7702053749293748	src/main/cpp/reanimated/CMakeFiles/reanimated.dir/de26f8cead47227ca676f03d1d571aaf/NativeReanimatedModule.cpp.o	766335efc5903531
13582	21147	7702053754919381	src/main/cpp/reanimated/CMakeFiles/reanimated.dir/android/OnLoad.cpp.o	904126f515759867
13223	23651	7702053779562060	src/main/cpp/reanimated/CMakeFiles/reanimated.dir/android/NativeProxy.cpp.o	1df6b4a36d4dee32
23651	23824	7702053781455389	../../../../build/intermediates/cxx/Debug/252f2v2v/obj/armeabi-v7a/libreanimated.so	5b7694428a3c8e67
4	29	0	D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-reanimated/android/.cxx/Debug/252f2v2v/armeabi-v7a/CMakeFiles/cmake.verify_globs	4bcba20779f7704f
