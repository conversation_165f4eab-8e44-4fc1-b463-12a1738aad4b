<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\node_modules\react-native-get-random-values\android\src\main\assets"/></dataSet><dataSet config="debug" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\node_modules\react-native-get-random-values\android\src\debug\assets"/></dataSet><dataSet config="generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\node_modules\react-native-get-random-values\android\build\intermediates\shader_assets\debug\compileDebugShaders\out"/></dataSet></merger>