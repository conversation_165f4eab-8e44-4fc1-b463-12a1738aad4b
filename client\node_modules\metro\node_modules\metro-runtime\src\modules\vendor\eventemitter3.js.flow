/**
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 * @flow
 * @format
 */
'use strict';

declare type ListenerFn = (...args: any[]) => mixed;
declare class EventEmitter {
  static constructor(): EventEmitter;
  static prefixed: string | boolean;
  eventNames(): (string | Symbol)[];
  listeners(event: string | Symbol): ListenerFn[];
  listenerCount(event: string | Symbol): number;
  on(event: string | Symbol, listener: ListenerFn, context?: any): this;
  addListener(
    event: string | Symbol,
    listener: ListenerFn,
    context?: any,
  ): this;
  once(event: string | Symbol, listener: ListenerFn, context?: any): this;
  removeAllListeners(event?: string | Symbol): this;
  removeListener(
    event: string | Symbol,
    listener?: ListenerFn,
    context?: any,
    once?: boolean,
  ): this;
  off(
    event: string | Symbol,
    listener?: ListenerFn,
    context?: any,
    once?: boolean,
  ): this;
  emit(event: string, ...params?: any[]): this;
}
declare module.exports: Class<EventEmitter>;
